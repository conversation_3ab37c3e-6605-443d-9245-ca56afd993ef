import asyncio
import json
from typing import Any, Sequence
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
import mcp.types as types
from knowledge import Knowledge

# Initialize the knowledge instance
knowledge_instance = Knowledge()

# Create MCP server
server = Server("knowledge-server")

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """List available tools."""
    return [
        Tool(
            name="get_knowledge_data",
            description="获取知识数据，基于查询文本搜索相关文档并返回格式化的知识内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "查询文本，用于搜索相关文档"
                    }
                },
                "required": ["query"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict[str, Any]) -> list[types.TextContent]:
    """Handle tool calls."""
    if name == "get_knowledge_data":
        try:
            query = arguments.get("query")
            
            if not query:
                return [types.TextContent(
                    type="text",
                    text="错误：查询文本不能为空"
                )]
            
            # Call the knowledge service
            result = knowledge_instance.get_knowledge_data(query, 0)
            
            return [types.TextContent(
                type="text", 
                text=result
            )]
            
        except Exception as e:
            return [types.TextContent(
                type="text",
                text=f"调用知识服务时发生错误: {str(e)}"
            )]
    else:
        raise ValueError(f"Unknown tool: {name}")

async def main():
    """Main entry point for the MCP server."""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="knowledge-server",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                )
            )
        )

if __name__ == "__main__":
    asyncio.run(main())